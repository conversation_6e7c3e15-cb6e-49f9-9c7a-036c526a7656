<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('styles') ?>
<style>


@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Additional visual effects */
.file-processing-overlay::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 50% 50%, rgba(0, 123, 255, 0.1) 0%, transparent 70%);
    animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 0.6; transform: scale(1.05); }
}

/* Top Progress Bar */
.top-progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #007bff, #28a745);
    color: white;
    padding: 1rem;
    z-index: 9998;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    animation: slideDown 0.5s ease-out;
}

.progress-line {
    width: 100%;
    height: 4px;
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.progress-line-fill {
    height: 100%;
    background: white;
    border-radius: 2px;
    width: 0%;
    animation: progressPulse 2s ease-in-out infinite;
    transition: width 0.5s ease;
}

.progress-text {
    text-align: center;
    font-weight: 500;
    font-size: 0.9rem;
}

@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes progressPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

/* Processing indicator animation */
.fa-robot {
    animation: robotPulse 2s ease-in-out infinite;
}

@keyframes robotPulse {
    0%, 100% {
        opacity: 0.7;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.1);
    }
}
</style>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-2">Profile Management</h1>
                    <p class="text-muted">Update your profile information in different sections</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Left Column - Profile Photo and Quick Links -->
        <div class="col-md-3">
            <div class="card mb-4">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <?php if (!empty($applicant['id_photo_path'])): ?>
                            <img src="<?= base_url($applicant['id_photo_path']) ?>" class="rounded-circle img-thumbnail" style="width: 150px; height: 150px; object-fit: cover;">
                        <?php else: ?>
                            <div class="rounded-circle bg-light d-inline-flex align-items-center justify-content-center" style="width: 150px; height: 150px;">
                                <i class="fas fa-user fa-4x text-secondary"></i>
                            </div>
                        <?php endif; ?>
                    </div>
                    <h5 class="mb-1"><?= esc($applicant['first_name']) ?> <?= esc($applicant['last_name']) ?></h5>
                    <p class="text-muted mb-3"><?= esc($applicant['current_position'] ?? 'Position not set') ?></p>

                    <!-- Quick Upload Photo Button -->
                    <form action="<?= base_url('applicant/profile/upload-photo') ?>" method="post" enctype="multipart/form-data" id="photoForm" class="mb-3">
                        <?= csrf_field() ?>
                        <div class="d-grid">
                            <label class="btn btn-outline-primary">
                                <i class="fas fa-camera me-2"></i>Change Photo
                                <input type="file" name="id_photo" class="d-none" accept="image/*">
                            </label>
                        </div>
                    </form>

                    <!-- Navigation Links -->
                    <div class="list-group">
                        <!-- 1. Personal Information -->
                        <a href="#personalInfo" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-user me-3"></i>
                                <div>
                                    <h6 class="mb-0">Personal Information</h6>
                                    <small class="text-muted">Basic details and contact info</small>
                                </div>
                            </div>
                        </a>
                        <!-- 2. Documents & ID -->
                        <a href="#documents" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-id-card me-3"></i>
                                <div>
                                    <h6 class="mb-0">Documents & ID</h6>
                                    <small class="text-muted">Identification and records</small>
                                </div>
                            </div>
                        </a>
                        <!-- 3. Employment -->
                        <a href="#employment" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-briefcase me-3"></i>
                                <div>
                                    <h6 class="mb-0">Employment</h6>
                                    <small class="text-muted">Current work information</small>
                                </div>
                            </div>
                        </a>
                        <!-- 4. Work Experience -->
                        <a href="#experiences" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-history me-3"></i>
                                <div>
                                    <h6 class="mb-0">Work Experience</h6>
                                    <small class="text-muted">Previous employment history</small>
                                </div>
                            </div>
                        </a>
                        <!-- 5. Education -->
                        <a href="#education" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-graduation-cap me-3"></i>
                                <div>
                                    <h6 class="mb-0">Education</h6>
                                    <small class="text-muted">Academic qualifications</small>
                                </div>
                            </div>
                        </a>
                        <!-- 6. Files -->
                        <a href="#files" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-alt me-3"></i>
                                <div>
                                    <h6 class="mb-0">Documents & Files</h6>
                                    <small class="text-muted">Upload and manage files</small>
                                </div>
                            </div>
                        </a>
                        <!-- 7. Family Information -->
                        <a href="#family" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-users me-3"></i>
                                <div>
                                    <h6 class="mb-0">Family Information</h6>
                                    <small class="text-muted">Family details and dependents</small>
                                </div>
                            </div>
                        </a>
                        <!-- 8. Additional Information -->
                        <a href="#additional" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-star me-3"></i>
                                <div>
                                    <h6 class="mb-0">Achievements</h6>
                                    <small class="text-muted">Awards and accomplishments</small>
                                </div>
                            </div>
                        </a>
                        <!-- 9. Security Settings -->
                        <a href="#security" class="list-group-item list-group-item-action">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-lock me-3"></i>
                                <div>
                                    <h6 class="mb-0">Security Settings</h6>
                                    <small class="text-muted">Password and account security</small>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column - Forms -->
        <div class="col-md-9">
            <!-- Personal Information Section -->
            <div class="card mb-4" id="personalInfo">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Personal Information</h5>
                    <!-- <button type="button" class="btn btn-sm btn-outline-primary" onclick="toggleEdit('personalForm')">
                        <i class="fas fa-edit me-2"></i>Edit
                    </button> -->
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-personal') ?>" method="post" id="personalForm" class="needs-validation" novalidate>
                        <?= csrf_field() ?>
                        <input type="hidden" name="scroll_position" id="personal_scroll_position">
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">First Name</label>
                                <input type="text" class="form-control" name="first_name" value="<?= esc($applicant['first_name'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Last Name</label>
                                <input type="text" class="form-control" name="last_name" value="<?= esc($applicant['last_name'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Gender</label>
                                <select class="form-select" name="gender" required>
                                    <option value="">Select Gender</option>
                                    <option value="male" <?= ($applicant['gender'] ?? '') == 'male' ? 'selected' : '' ?>>Male</option>
                                    <option value="female" <?= ($applicant['gender'] ?? '') == 'female' ? 'selected' : '' ?>>Female</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" name="dobirth" value="<?= esc($applicant['dobirth'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Contact Number</label>
                                <input type="tel" class="form-control" name="contact_details" value="<?= esc($applicant['contact_details'] ?? '') ?>" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Email Address</label>
                                <input type="email" class="form-control" value="<?= esc($applicant['email'] ?? '') ?>" readonly>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Current Address</label>
                                <textarea class="form-control" name="location_address" rows="3"><?= esc($applicant['location_address'] ?? '') ?></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Place of Origin</label>
                                <input type="text" class="form-control" name="place_of_origin" value="<?= esc($applicant['place_of_origin'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Citizenship</label>
                                <input type="text" class="form-control" name="citizenship" value="<?= esc($applicant['citizenship'] ?? '') ?>">
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Save Changes</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Documents & ID Section -->
            <div class="card mb-4" id="documents">
                <div class="card-header">
                    <h5 class="card-title mb-0">Identifications</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-documents') ?>" method="post" id="documentsForm">
                        <?= csrf_field() ?>
                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">ID Numbers (NID, Passport, Driving License)</label>
                                <textarea class="form-control" name="id_numbers" rows="2" placeholder="Enter your identification numbers..."><?= esc($applicant['id_numbers'] ?? '') ?></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Criminal Record (if any)</label>
                                <textarea class="form-control" name="offence_convicted" rows="2" placeholder="Leave blank if not convicted"><?= esc($applicant['offence_convicted'] ?? '') ?></textarea>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Update Identification</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Employment Information -->
            <div class="card mb-4" id="employment">
                <div class="card-header">
                    <h5 class="card-title mb-0">Employment Information</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-employment') ?>" method="post" id="employmentForm">
                        <?= csrf_field() ?>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Current Employer</label>
                                <input type="text" class="form-control" name="current_employer" value="<?= esc($applicant['current_employer'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Current Position</label>
                                <input type="text" class="form-control" name="current_position" value="<?= esc($applicant['current_position'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Current Salary</label>
                                <input type="text" class="form-control" name="current_salary" value="<?= esc($applicant['current_salary'] ?? '') ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">How did you hear about us?</label>
                                <select class="form-select" name="how_did_you_hear_about_us">
                                    <option value="">Select Source</option>
                                    <option value="newspaper" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'newspaper' ? 'selected' : '' ?>>Newspaper</option>
                                    <option value="social_media" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'social_media' ? 'selected' : '' ?>>Social Media</option>
                                    <option value="website" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'website' ? 'selected' : '' ?>>Website</option>
                                    <option value="referral" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'referral' ? 'selected' : '' ?>>Referral</option>
                                    <option value="other" <?= ($applicant['how_did_you_hear_about_us'] ?? '') == 'other' ? 'selected' : '' ?>>Other</option>
                                </select>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Update Employment</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Work Experience -->
            <div class="card mb-4" id="experiences">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Work Experience</h5>
                    <button type="button" class="btn btn-sm btn-primary" onclick="addExperience()">
                        <i class="fas fa-plus me-2"></i>Add Experience
                    </button>
                </div>
                <div class="card-body">
                    <div id="experiencesList">
                        <?php if (empty($experiences)): ?>
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-briefcase fa-3x mb-3"></i>
                                <p>No work experience added yet.</p>
                            </div>
                        <?php else: ?>
                            <?php foreach ($experiences as $exp): ?>
                                <div class="experience-item mb-4 border-bottom pb-3" data-id="<?= $exp['id'] ?>">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <h5 class="mb-1"><?= esc($exp['position']) ?></h5>
                                            <h6 class="text-primary mb-1"><?= esc($exp['employer']) ?></h6>
                                            <p class="text-muted small mb-2">
                                                <?= date('M Y', strtotime($exp['date_from'])) ?> -
                                                <?php
                                                    if (empty($exp['date_to']) || $exp['date_to'] == '0000-00-00' || $exp['date_to'] == null) {
                                                        echo 'Present';
                                                    } else {
                                                        echo date('M Y', strtotime($exp['date_to']));
                                                    }
                                                ?>
                                            </p>
                                        </div>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editModal<?= $exp['id'] ?>">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $exp['id'] ?>">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <?php if (!empty($exp['employer_contacts_address'])): ?>
                                        <p class="text-muted small mb-2">
                                            <i class="fas fa-map-marker-alt me-2"></i><?= esc($exp['employer_contacts_address']) ?>
                                        </p>
                                    <?php endif; ?>
                                    <?php if (!empty($exp['work_description'])): ?>
                                        <p class="mb-2"><?= nl2br(esc($exp['work_description'])) ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($exp['achievements'])): ?>
                                        <div class="achievements">
                                            <strong class="text-success">Key Achievements:</strong>
                                            <p class="mb-0"><?= nl2br(esc($exp['achievements'])) ?></p>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Edit Modal for each experience -->
                                <div class="modal fade" id="editModal<?= $exp['id'] ?>" tabindex="-1" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Edit Work Experience</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="editForm<?= $exp['id'] ?>">
                                                    <?= csrf_field() ?>
                                                    <input type="hidden" name="id" value="<?= $exp['id'] ?>">
                                                    <div class="mb-3">
                                                        <label class="form-label">Position</label>
                                                        <input type="text" class="form-control" name="position" value="<?= esc($exp['position']) ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Employer</label>
                                                        <input type="text" class="form-control" name="employer" value="<?= esc($exp['employer']) ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Employer Address & Contacts</label>
                                                        <textarea class="form-control" name="employer_contacts_address" rows="2"><?= esc($exp['employer_contacts_address']) ?></textarea>
                                                    </div>
                                                    <div class="row mb-3">
                                                        <div class="col-md-6">
                                                            <label class="form-label">Date From</label>
                                                            <input type="date" class="form-control" name="date_from" value="<?= !empty($exp['date_from']) && $exp['date_from'] != '0000-00-00' ? esc($exp['date_from']) : '' ?>" required>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <label class="form-label">Date To</label>
                                                            <input type="date" class="form-control" name="date_to" value="<?= !empty($exp['date_to']) && $exp['date_to'] != '0000-00-00' ? esc($exp['date_to']) : '' ?>">
                                                            <small class="text-muted">Leave blank if current position</small>
                                                        </div>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Work Description</label>
                                                        <textarea class="form-control" name="work_description" rows="3"><?= esc($exp['work_description']) ?></textarea>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Achievements</label>
                                                        <textarea class="form-control" name="achievements" rows="3"><?= esc($exp['achievements']) ?></textarea>
                                                    </div>
                                                </form>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn btn-primary" onclick="updateExperience(<?= $exp['id'] ?>)">Update</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Modal for each experience -->
                                <div class="modal fade" id="deleteModal<?= $exp['id'] ?>" tabindex="-1" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Delete Work Experience</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete this work experience at <?= esc($exp['employer']) ?>?</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn btn-danger" onclick="confirmDelete(<?= $exp['id'] ?>)">Delete</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Education -->
            <div class="card mb-4" id="education">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Education</h5>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addEducationModal">
                        <i class="fas fa-plus me-2"></i>Add Education
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($education)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-graduation-cap fa-3x mb-3"></i>
                            <p>No education records found.</p>
                        </div>
                    <?php else: ?>
                        <div class="timeline">
                            <?php foreach ($education as $edu): ?>
                                <div class="timeline-item mb-4 border-bottom pb-3">
                                    <div class="timeline-content">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h5 class="mb-1"><?= esc($edu['institution']) ?></h5>
                                                <p class="mb-1"><?= esc($edu['course']) ?></p>
                                                <p class="text-muted mb-1">
                                                    <?= date('M Y', strtotime($edu['date_from'])) ?> -
                                                    <?php if (empty($edu['date_to']) || $edu['date_to'] == '0000-00-00'): ?>
                                                        Present
                                                    <?php else: ?>
                                                        <?= date('M Y', strtotime($edu['date_to'])) ?>
                                                    <?php endif; ?>
                                                </p>
                                                <p class="mb-1">
                                                    <?php
                                                    $education_level_name = '';
                                                    if (!empty($education_data)) {
                                                        foreach ($education_data as $edu_level) {
                                                            if ($edu_level['id'] == $edu['education_level']) {
                                                                $education_level_name = $edu_level['name'];
                                                                break;
                                                            }
                                                        }
                                                    }
                                                    if (empty($education_level_name)) {
                                                        $education_level_name = $education_levels[$edu['education_level']] ?? 'Unknown';
                                                    }
                                                    ?>
                                                    <span class="badge bg-info"><?= esc($education_level_name) ?></span>
                                                    <?php if (!empty($edu['units'])): ?>
                                                        <span class="text-muted">(<?= nl2br(esc($edu['units'])) ?> units)</span>
                                                    <?php endif; ?>
                                                </p>
                                            </div>
                                            <div>
                                                <button type="button" class="btn btn-sm btn-outline-primary me-1"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#editEducationModal<?= $edu['id'] ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        data-bs-toggle="modal"
                                                        data-bs-target="#deleteEducationModal<?= $edu['id'] ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Edit Education Modal -->
                                <div class="modal fade" id="editEducationModal<?= $edu['id'] ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Edit Education</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <form id="editEducationForm<?= $edu['id'] ?>">
                                                    <?= csrf_field() ?>
                                                    <input type="hidden" name="id" value="<?= $edu['id'] ?>">
                                                    <div class="mb-3">
                                                        <label class="form-label">Institution</label>
                                                        <input type="text" class="form-control" name="institution" value="<?= esc($edu['institution']) ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Course/Program</label>
                                                        <input type="text" class="form-control" name="course" value="<?= esc($edu['course']) ?>" required>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Education Level</label>
                                                        <select class="form-select" name="education_level" required>
                                                            <option value="">Select Level</option>
                                                            <?php if (!empty($education_data)): ?>
                                                                <?php foreach ($education_data as $edu_level): ?>
                                                                    <option value="<?= $edu_level['id'] ?>" <?= isset($edu['education_level']) && $edu['education_level'] == $edu_level['id'] ? 'selected' : '' ?>>
                                                                        <?= esc($edu_level['name']) ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            <?php else: ?>
                                                                <?php foreach ($education_levels as $key => $level): ?>
                                                                    <option value="<?= $key ?>" <?= isset($edu['education_level']) && $edu['education_level'] == $key ? 'selected' : '' ?>>
                                                                        <?= esc($level) ?>
                                                                    </option>
                                                                <?php endforeach; ?>
                                                            <?php endif; ?>
                                                        </select>
                                                    </div>
                                                    <div class="mb-3">
                                                        <label class="form-label">Units (Optional)</label>
                                                        <textarea class="form-control" name="units" rows="3" placeholder="List the units/subjects under this course..."><?= esc($edu['units']) ?></textarea>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">Date From</label>
                                                            <input type="date" class="form-control" name="date_from"
                                                                   value="<?= $edu['date_from'] != '0000-00-00' ? $edu['date_from'] : '' ?>" required>
                                                        </div>
                                                        <div class="col-md-6 mb-3">
                                                            <label class="form-label">Date To</label>
                                                            <input type="date" class="form-control" name="date_to"
                                                                   value="<?= $edu['date_to'] != '0000-00-00' ? $edu['date_to'] : '' ?>">
                                                            <small class="text-muted">Leave blank if current</small>
                                                        </div>
                                                    </div>
                                                    <div class="text-end">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                        <button type="button" class="btn btn-primary" onclick="updateEducation(<?= $edu['id'] ?>)">Save Changes</button>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Delete Education Modal -->
                                <div class="modal fade" id="deleteEducationModal<?= $edu['id'] ?>" tabindex="-1">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Delete Education Record</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <p>Are you sure you want to delete your education record at <?= esc($edu['institution']) ?>?</p>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                <button type="button" class="btn btn-danger" onclick="confirmDeleteEducation(<?= $edu['id'] ?>)">Delete</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Files Section -->
            <div class="card mb-4" id="files">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Documents & Files</h5>
                    <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addFileModal">
                        <i class="fas fa-plus me-2"></i>Upload File
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($files)): ?>
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-file fa-3x mb-3"></i>
                            <p>No files uploaded yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>File</th>
                                        <th>Description</th>
                                        <th>Type</th>
                                        <th>Text Extracted</th>
                                        <th>Upload Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($files as $file): ?>
                                        <?php
                                        $file_extension = strtolower(pathinfo($file['file_path'], PATHINFO_EXTENSION));
                                        $file_icon = '';
                                        $file_color = '';

                                        switch($file_extension) {
                                            case 'pdf':
                                                $file_icon = 'fas fa-file-pdf';
                                                $file_color = 'text-danger';
                                                break;
                                            case 'doc':
                                            case 'docx':
                                                $file_icon = 'fas fa-file-word';
                                                $file_color = 'text-primary';
                                                break;
                                            case 'jpg':
                                            case 'jpeg':
                                            case 'png':
                                                $file_icon = 'fas fa-file-image';
                                                $file_color = 'text-success';
                                                break;
                                            default:
                                                $file_icon = 'fas fa-file';
                                                $file_color = 'text-secondary';
                                        }
                                        ?>
                                        <tr data-file-id="<?= $file['id'] ?>">
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <i class="<?= $file_icon ?> <?= $file_color ?> me-2"></i>
                                                    <div>
                                                        <div class="fw-medium"><?= esc($file['file_title']) ?></div>
                                                        
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="text-muted"><?= esc($file['file_description'] ?: 'No description') ?></span>
                                            </td>
                                            <td>
                                                <span class="badge bg-light text-dark"><?= strtoupper($file_extension) ?></span>
                                            </td>
                                            <td>
                                                <?php if (!empty($file['file_extracted_texts'])): ?>
                                                    <button type="button" class="btn btn-sm btn-outline-info view-extracted-text"
                                                            data-bs-toggle="modal"
                                                            data-bs-target="#extractedTextModal"
                                                            data-file-id="<?= $file['id'] ?>">
                                                        <i class="fas fa-eye me-1"></i>View Text
                                                    </button>
                                                <?php else: ?>
                                                    <span class="text-muted">
                                                        <i class="fas fa-robot me-1"></i>AI extracting text...
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('M d, Y', strtotime($file['created_at'])) ?></td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?= base_url($file['file_path']) ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       target="_blank"
                                                       title="View/Download">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-secondary"
                                                            onclick="editFile(<?= $file['id'] ?>, '<?= esc($file['file_title']) ?>', '<?= esc($file['file_description']) ?>')"
                                                            title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <button type="button"
                                                            class="btn btn-sm btn-outline-danger"
                                                            onclick="confirmDeleteFile(<?= $file['id'] ?>)"
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Add File Modal -->
            <div class="modal fade" id="addFileModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Upload File</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="uploadFileForm" class="needs-validation" novalidate>
                                <?= csrf_field() ?>
                                <div class="mb-3">
                                    <label class="form-label">File Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="file_title" required>
                                    <div class="invalid-feedback">
                                        Please provide a file title
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Description (Optional)</label>
                                    <textarea class="form-control" name="file_description" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">File <span class="text-danger">*</span></label>
                                    <input type="file" class="form-control" name="file" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" required>
                                    <div class="invalid-feedback">
                                        Please select a valid file
                                    </div>
                                    <small class="text-muted">Allowed: PDF, DOC, DOCX, JPG, JPEG, PNG (Max: 25MB)</small>
                                    <div class="alert alert-info mt-2 mb-0">
                                        <i class="fas fa-robot me-2"></i>
                                        <small><strong>AI Text Extraction:</strong> Text will be automatically extracted from your uploaded files using advanced AI technology. For large PDFs (>10 pages), the document will be split into smaller chunks for optimal processing.</small>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="button" class="btn btn-primary" id="uploadBtn">
                                <i class="fas fa-upload me-2"></i>Upload & Process
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Progress Modal -->
            <div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static" data-bs-keyboard="false">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header border-0">
                            <h5 class="modal-title">
                                <i class="fas fa-robot me-2 text-primary"></i>
                                AI Document Processing
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <!-- File Info -->
                            <div class="mb-4">
                                <div class="file-icon mb-3">
                                    <i class="fas fa-file-alt fa-3x text-muted"></i>
                                </div>
                                <h6 class="mb-1" id="progressFileName">Processing file...</h6>
                                <small class="text-muted" id="progressFileSize"></small>
                            </div>

                            <!-- Progress Bar -->
                            <div class="progress mb-3" style="height: 8px;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-primary"
                                     role="progressbar"
                                     style="width: 0%"
                                     id="progressBar">
                                </div>
                            </div>

                            <!-- Progress Percentage -->
                            <div class="mb-3">
                                <span class="badge bg-primary fs-6" id="progressPercent">0%</span>
                            </div>

                            <!-- Current Step -->
                            <div class="mb-4">
                                <div class="d-flex align-items-center justify-content-center">
                                    <div class="spinner-border spinner-border-sm text-primary me-2" role="status" id="progressSpinner">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span id="progressMessage">Initializing...</span>
                                </div>
                            </div>

                            <!-- Processing Steps -->
                            <div class="row text-center">
                                <div class="col-3">
                                    <div class="step-icon mb-2" id="stepUpload">
                                        <i class="fas fa-upload fa-lg text-muted"></i>
                                    </div>
                                    <small class="text-muted">Upload</small>
                                </div>
                                <div class="col-3">
                                    <div class="step-icon mb-2" id="stepAnalyze">
                                        <i class="fas fa-search fa-lg text-muted"></i>
                                    </div>
                                    <small class="text-muted">Analyze</small>
                                </div>
                                <div class="col-3">
                                    <div class="step-icon mb-2" id="stepExtract">
                                        <i class="fas fa-robot fa-lg text-muted"></i>
                                    </div>
                                    <small class="text-muted">Extract</small>
                                </div>
                                <div class="col-3">
                                    <div class="step-icon mb-2" id="stepSave">
                                        <i class="fas fa-save fa-lg text-muted"></i>
                                    </div>
                                    <small class="text-muted">Save</small>
                                </div>
                            </div>

                            <!-- Chunk Processing Info (for large PDFs) -->
                            <div class="mt-3 d-none" id="chunkInfo">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small id="chunkMessage">Processing document in chunks...</small>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer border-0 justify-content-center">
                            <button type="button" class="btn btn-outline-secondary d-none" id="progressCloseBtn">
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit File Modal -->
            <div class="modal fade" id="editFileModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Edit File Information</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form action="<?= base_url('applicant/profile/update-file') ?>" method="post" id="editFileForm">
                                <?= csrf_field() ?>
                                <input type="hidden" name="file_id" id="edit_file_id">
                                <input type="hidden" name="scroll_position" id="edit_scroll_position">
                                <div class="mb-3">
                                    <label class="form-label">File Title <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" name="file_title" id="edit_file_title" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Description (Optional)</label>
                                    <textarea class="form-control" name="file_description" id="edit_file_description" rows="3"></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                            <button type="submit" form="editFileForm" class="btn btn-primary">Update</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Top Progress Bar -->
            <div id="topProgressBar" class="top-progress-bar d-none">
                <div class="progress-line">
                    <div class="progress-line-fill"></div>
                </div>
                <div class="progress-text">
                    <i class="fas fa-robot me-2"></i>
                    <span id="topProgressText">Processing file and extracting complete text from all pages...</span>
                </div>
            </div>



            <!-- File Processing Overlay (Backup) -->
            <div id="fileProcessingOverlay" class="file-processing-overlay d-none">
                <div class="processing-content">
                    <div class="fancy-spinner">
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-ring"></div>
                        <div class="spinner-center">
                            <i class="fas fa-file-upload"></i>
                        </div>
                    </div>
                    <h4 class="processing-title">Processing Your File</h4>
                    <p class="processing-message">
                        <span id="processingStepOverlay">Uploading file...</span>
                    </p>
                    <div class="processing-steps">
                        <div class="step" id="step1Overlay">
                            <i class="fas fa-upload"></i>
                            <span>Upload</span>
                        </div>
                        <div class="step" id="step2Overlay">
                            <i class="fas fa-robot"></i>
                            <span>AI Analysis</span>
                        </div>
                        <div class="step" id="step3Overlay">
                            <i class="fas fa-check"></i>
                            <span>Complete</span>
                        </div>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFillOverlay"></div>
                    </div>
                </div>
            </div>

            <!-- Extracted Text Modal -->
            <div class="modal fade" id="extractedTextModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Extracted Text</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label fw-bold">File:</label>
                                <span id="extracted_text_filename"></span>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label class="form-label fw-bold mb-0">Extracted Content:</label>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <input type="radio" class="btn-check" name="viewMode" id="markdownView" checked>
                                        <label class="btn btn-outline-secondary" for="markdownView">Formatted</label>

                                        <input type="radio" class="btn-check" name="viewMode" id="rawView">
                                        <label class="btn btn-outline-secondary" for="rawView">Raw</label>
                                    </div>
                                </div>
                                <div class="border rounded p-3" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa;">
                                    <div id="extracted_text_formatted" style="display: block; line-height: 1.6;"></div>
                                    <pre id="extracted_text_raw" style="white-space: pre-wrap; margin: 0; font-family: inherit; display: none; line-height: 1.5;"></pre>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-primary" onclick="copyExtractedText()">
                                <i class="fas fa-copy me-2"></i>Copy Text
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Information -->
            <div class="card mb-4" id="family">
                <div class="card-header">
                    <h5 class="card-title mb-0">Family Information</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-family') ?>" method="post" id="familyForm">
                        <?= csrf_field() ?>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label class="form-label">Marital Status</label>
                                <select class="form-select" name="marital_status" id="maritalStatus">
                                    <option value="">Select Status</option>
                                    <option value="single" <?= ($applicant['marital_status'] ?? '') == 'single' ? 'selected' : '' ?>>Single</option>
                                    <option value="married" <?= ($applicant['marital_status'] ?? '') == 'married' ? 'selected' : '' ?>>Married</option>
                                    <option value="divorced" <?= ($applicant['marital_status'] ?? '') == 'divorced' ? 'selected' : '' ?>>Divorced</option>
                                    <option value="widowed" <?= ($applicant['marital_status'] ?? '') == 'widowed' ? 'selected' : '' ?>>Widowed</option>
                                    <option value="separated" <?= ($applicant['marital_status'] ?? '') == 'separated' ? 'selected' : '' ?>>Separated</option>
                                </select>
                            </div>
                            <div class="col-md-6 marriage-details" style="display: none;">
                                <label class="form-label">Date of Marriage</label>
                                <input type="date" class="form-control" name="date_of_marriage" value="<?= esc($applicant['date_of_marriage'] ?? '') ?>">
                            </div>
                            <div class="col-12 marriage-details" style="display: none;">
                                <label class="form-label">Spouse's Employer</label>
                                <input type="text" class="form-control" name="spouse_employer" value="<?= esc($applicant['spouse_employer'] ?? '') ?>" placeholder="Employer name or public servant file number if government employee">
                            </div>
                            <div class="col-12">
                                <label class="form-label">Children Information</label>
                                <div id="childrenContainer">
                                    <?php
                                    $children = json_decode($applicant['children'] ?? '[]', true);
                                    foreach ($children as $index => $child):
                                    ?>
                                    <div class="row g-2 mb-2 child-entry">
                                        <div class="col-md-4">
                                            <input type="text" class="form-control" name="children[<?= $index ?>][name]" value="<?= esc($child['name']) ?>" placeholder="Child's Name">
                                        </div>
                                        <div class="col-md-4">
                                            <input type="date" class="form-control" name="children[<?= $index ?>][dob]" value="<?= esc($child['dob']) ?>">
                                        </div>
                                        <div class="col-md-3">
                                            <select class="form-select" name="children[<?= $index ?>][gender]">
                                                <option value="">Gender</option>
                                                <option value="Male" <?= $child['gender'] == 'Male' ? 'selected' : '' ?>>Male</option>
                                                <option value="Female" <?= $child['gender'] == 'Female' ? 'selected' : '' ?>>Female</option>
                                            </select>
                                        </div>
                                        <div class="col-md-1">
                                            <button type="button" class="btn btn-danger btn-sm remove-child"><i class="fas fa-times"></i></button>
                                        </div>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <button type="button" class="btn btn-outline-secondary btn-sm mt-2" id="addChild">
                                    <i class="fas fa-plus me-2"></i>Add Child
                                </button>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Update Family Information</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Additional Information -->
            <div class="card mb-4" id="additional">
                <div class="card-header">
                    <h5 class="card-title mb-0">Achievements & References</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/update-additional') ?>" method="post" id="additionalForm">
                        <?= csrf_field() ?>
                        <input type="hidden" name="scroll_position" id="additional_scroll_position">
                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">Publications</label>
                                <textarea class="form-control" name="publications" rows="3" placeholder="List your publications with dates..."><?= esc($applicant['publications'] ?? '') ?></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Awards & Achievements</label>
                                <textarea class="form-control" name="awards" rows="3" placeholder="List your awards and achievements..."><?= esc($applicant['awards'] ?? '') ?></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label">Referees</label>
                                <textarea class="form-control" name="referees" rows="4" placeholder="Enter referee details (name, address, contact details)..."><?= esc($applicant['referees'] ?? '') ?></textarea>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Update Additional Information</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Security Settings -->
            <div class="card mb-4" id="security">
                <div class="card-header">
                    <h5 class="card-title mb-0">Security Settings</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('applicant/profile/change-password') ?>" method="post" id="passwordForm">
                        <?= csrf_field() ?>
                        <div class="row g-3">
                            <div class="col-md-12">
                                <label class="form-label">Current Password</label>
                                <input type="password" class="form-control" name="current_password" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">New Password</label>
                                <input type="password" class="form-control" name="new_password" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" name="confirm_password" required>
                            </div>
                        </div>
                        <div class="text-end mt-3">
                            <button type="submit" class="btn btn-primary">Change Password</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Experience Modal -->
<div class="modal fade" id="experienceModal" tabindex="-1" aria-labelledby="experienceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="experienceModalLabel">Add Work Experience</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="experienceForm">
                    <?= csrf_field() ?>
                    <input type="hidden" name="id" id="experience_id">
                    <div class="mb-3">
                        <label class="form-label">Position</label>
                        <input type="text" class="form-control" name="position" id="position" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Employer</label>
                        <input type="text" class="form-control" name="employer" id="employer" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Employer Address & Contacts</label>
                        <textarea class="form-control" name="employer_contacts_address" id="employer_contacts_address" rows="2"></textarea>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" name="date_from" id="date_from" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" name="date_to" id="date_to">
                            <small class="text-muted">Leave blank if current position</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Work Description</label>
                        <textarea class="form-control" name="work_description" id="work_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Achievements</label>
                        <textarea class="form-control" name="achievements" id="achievements" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveExperience">Save</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Education Modal -->
<div class="modal fade" id="addEducationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Education</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <?php
                // Initialize $edu with default values
                $edu = [
                    'education_level' => '',
                    'date_to' => '',
                    'units' => ''
                ];
                ?>
                <form id="addEducationForm">
                    <?= csrf_field() ?>
                    <div class="mb-3">
                        <label class="form-label">Institution Name</label>
                        <input type="text" class="form-control" name="institution" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Course/Program</label>
                        <input type="text" class="form-control" name="course" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Education Level</label>
                        <select class="form-select" name="education_level" required>
                            <option value="">Select Level</option>
                            <?php if (!empty($education_data)): ?>
                                <?php foreach ($education_data as $edu_level): ?>
                                    <option value="<?= $edu_level['id'] ?>" <?= isset($edu['education_level']) && $edu['education_level'] == $edu_level['id'] ? 'selected' : '' ?>>
                                        <?= esc($edu_level['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <?php foreach ($education_levels as $key => $level): ?>
                                    <option value="<?= $key ?>" <?= isset($edu['education_level']) && $edu['education_level'] == $key ? 'selected' : '' ?>>
                                        <?= esc($level) ?>
                                    </option>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Units (Optional)</label>
                        <textarea class="form-control" name="units" rows="3" placeholder="List the units/subjects under this course..."></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Date From</label>
                            <input type="date" class="form-control" name="date_from" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Date To</label>
                            <input type="date" class="form-control" name="date_to"
                                   value="<?= $edu['date_to'] != '0000-00-00' ? $edu['date_to'] : '' ?>">
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="addEducation()">Add Education</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
// Add experiences data to JavaScript context
const experiences = <?= json_encode($experiences ?? []) ?>;

// Add files data to JavaScript context
const filesData = <?= json_encode($files ?? []) ?>;

// Storage access error handling
function handleStorageError() {
    try {
        localStorage.setItem('test', 'test');
        localStorage.removeItem('test');
    } catch (e) {
        console.warn('Local storage is not available. Some features might be limited.');
    }
}

// Save current scroll position before form submission
function saveScrollPosition() {
    const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;
    localStorage.setItem('profileScrollPosition', scrollPosition);
}

// Restore scroll position after page load
function restoreScrollPosition() {
    // Check for scroll parameter in URL first
    const urlParams = new URLSearchParams(window.location.search);
    const urlScrollPosition = urlParams.get('scroll');

    if (urlScrollPosition) {
        setTimeout(() => {
            window.scrollTo(0, parseInt(urlScrollPosition));
        }, 100);
        return;
    }

    // Fallback to localStorage
    const scrollPosition = localStorage.getItem('profileScrollPosition');
    if (scrollPosition) {
        setTimeout(() => {
            window.scrollTo(0, parseInt(scrollPosition));
        }, 100);
        localStorage.removeItem('profileScrollPosition');
    }
}

// Handle hash-based navigation (for section anchors)
function handleHashNavigation() {
    if (window.location.hash) {
        // Extract just the hash part without query parameters
        const hashPart = window.location.hash.split('?')[0];

        if (hashPart && hashPart.length > 1) {
            try {
                const target = document.querySelector(hashPart);
                if (target) {
                    setTimeout(() => {
                        target.scrollIntoView({ behavior: 'smooth', block: 'start' });
                    }, 100);
                }
            } catch (e) {
                console.warn('Invalid hash selector:', hashPart, e);
            }
        }
    }
}

// Format date for input fields
function formatDateForInput(dateString) {
    if (!dateString || dateString === '0000-00-00' || dateString === 'null') {
        return '';
    }
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
            return '';
        }
        return date.toISOString().split('T')[0];
    } catch (e) {
        return '';
    }
}

// Define these functions globally
function updateExperience(id) {
    const form = document.getElementById('editForm' + id);

    // Save scroll position before submission
    saveScrollPosition();

    // Submit form normally
    form.action = '<?= base_url('applicant/profile/update-experience') ?>';
    form.method = 'POST';
    form.submit();
}

function confirmDelete(id) {
    // Save scroll position before submission
    saveScrollPosition();

    // Create a form and submit it
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `<?= base_url('applicant/profile/delete-experience') ?>/${id}`;

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '<?= csrf_token() ?>';
    csrfInput.value = '<?= csrf_hash() ?>';
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    form.submit();
}

// Add the confirmDeleteEducation function
function confirmDeleteEducation(id) {
    // Save scroll position before submission
    saveScrollPosition();

    // Create a form and submit it
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `<?= base_url('applicant/profile/delete-education') ?>/${id}`;

    // Add CSRF token
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = '<?= csrf_token() ?>';
    csrfInput.value = '<?= csrf_hash() ?>';
    form.appendChild(csrfInput);

    document.body.appendChild(form);
    form.submit();
}

// Add the updateEducation function
function updateEducation(id) {
    const form = document.getElementById('editEducationForm' + id);

    // Save scroll position before submission
    saveScrollPosition();

    // Submit form normally
    form.action = '<?= base_url('applicant/profile/update-education') ?>';
    form.method = 'POST';
    form.submit();
}

// Add the addEducation function
function addEducation() {
    const form = document.getElementById('addEducationForm');

    // Save scroll position before submission
    saveScrollPosition();

    // Submit form normally
    form.action = '<?= base_url('applicant/profile/add-education') ?>';
    form.method = 'POST';
    form.submit();
}

// File upload form validation and submission
function validateFileUpload(form) {
    const fileTitle = form.querySelector('input[name="file_title"]').value.trim();
    const fileInput = form.querySelector('input[name="file"]');

    if (!fileTitle) {
        alert('Please provide a file title');
        return false;
    }

    if (!fileInput.files.length) {
        alert('Please select a file to upload');
        return false;
    }

    // Validate file size (25MB = 25600KB)
    const file = fileInput.files[0];
    if (file.size > 25600 * 1024) {
        alert('File size must be less than 25MB');
        return false;
    }

    // Validate file type
    const allowedTypes = ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'];
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
        alert('Please select a valid file type (PDF, DOC, DOCX, JPG, JPEG, PNG)');
        return false;
    }

    return true;
}

function resetUploadForm() {
    const form = document.getElementById('uploadFileForm');
    form.reset();
    form.classList.remove('was-validated');
}

function editFile(id, title, description) {
    // Populate the edit form
    document.getElementById('edit_file_id').value = id;
    document.getElementById('edit_file_title').value = title;
    document.getElementById('edit_file_description').value = description || '';

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('editFileModal'));
    modal.show();
}

function validateFileEdit(form) {
    const fileTitle = form.querySelector('input[name="file_title"]').value.trim();

    if (!fileTitle) {
        alert('Please provide a file title');
        return false;
    }

    return true;
}

// Check extraction progress via AJAX
function checkExtractionProgress(processId) {
    if (!processId) return;

    console.log('Starting background extraction progress check for:', processId);

    const checkInterval = setInterval(() => {
        fetch(`<?= base_url('applicant/profile/check-extraction-progress') ?>/${processId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Progress check:', data);

                if (data.status === 'completed') {
                    clearInterval(checkInterval);
                    console.log('Text extraction completed, refreshing page...');

                    // Refresh the page to show the extracted text
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);

                } else if (data.status === 'failed') {
                    clearInterval(checkInterval);
                    console.error('Text extraction failed:', data.error);

                    // Optionally show a toast notification for the error
                    // For now, just log it - the table will continue showing "Processing..."

                } else if (data.status === 'processing') {
                    console.log('Still processing:', data.message);
                    // The table already shows "Processing..." so no UI update needed
                }
            })
            .catch(error => {
                console.error('Error checking progress:', error);
                // Continue checking - don't stop on network errors
            });
    }, 5000); // Check every 5 seconds

    // Auto-stop checking after 5 minutes
    setTimeout(() => {
        clearInterval(checkInterval);
        console.log('Progress checking stopped after 5 minutes');
    }, 300000);
}







// Simple markdown renderer for basic formatting
function renderMarkdown(text) {
    if (!text) return '';

    let html = text;

    // Headers (process from largest to smallest to avoid conflicts)
    html = html.replace(/^### (.*$)/gim, '<h5 class="mt-3 mb-2">$1</h5>');
    html = html.replace(/^## (.*$)/gim, '<h4 class="mt-3 mb-2">$1</h4>');
    html = html.replace(/^# (.*$)/gim, '<h3 class="mt-3 mb-2">$1</h3>');

    // Bold and italic
    html = html.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/\*(.*?)\*/g, '<em>$1</em>');

    // Code
    html = html.replace(/`(.*?)`/g, '<code class="bg-light px-1 rounded">$1</code>');

    // Blockquotes
    html = html.replace(/^> (.*$)/gim, '<blockquote class="border-start border-3 border-secondary ps-3 text-muted mb-2">$1</blockquote>');

    // Simple table detection and conversion
    html = html.replace(/\|(.+)\|/g, function(match, content) {
        const cells = content.split('|').map(cell => cell.trim()).filter(cell => cell);
        const cellsHtml = cells.map(cell => `<td class="border px-2 py-1">${cell}</td>`).join('');
        return `<tr>${cellsHtml}</tr>`;
    });

    // Wrap table rows in table
    html = html.replace(/(<tr>.*?<\/tr>)(?:\s*<br>\s*<tr>.*?<\/tr>)*/g, function(match) {
        return `<table class="table table-sm table-bordered mb-3">${match.replace(/<br>/g, '')}</table>`;
    });

    // Lists
    html = html.replace(/^\* (.*$)/gim, '<li>$1</li>');
    html = html.replace(/^- (.*$)/gim, '<li>$1</li>');
    html = html.replace(/^(\d+)\. (.*$)/gim, '<li>$2</li>');

    // Line breaks
    html = html.replace(/\n/g, '<br>');

    // Wrap consecutive <li> elements in <ul>
    html = html.replace(/(<li>.*?<\/li>)(?:\s*<br>\s*<li>.*?<\/li>)*/g, function(match) {
        return '<ul class="mb-2">' + match.replace(/<br>/g, '') + '</ul>';
    });

    return html;
}

// Show extracted text in modal
function showExtractedText(filename, extractedText) {
    document.getElementById('extracted_text_filename').textContent = filename;
    document.getElementById('extracted_text_content').textContent = extractedText;
}

// Copy extracted text to clipboard
function copyExtractedText() {
    // Get text content based on current view mode
    const isFormattedView = document.getElementById('markdownView').checked;
    let textContent;

    if (isFormattedView) {
        // For formatted view, get the plain text from the raw content
        textContent = document.getElementById('extracted_text_raw').textContent;
    } else {
        // For raw view, get the text directly
        textContent = document.getElementById('extracted_text_raw').textContent;
    }

    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(textContent).then(() => {
            // Show success feedback
            const copyBtn = event.target.closest('button');
            const originalText = copyBtn.innerHTML;
            copyBtn.innerHTML = '<i class="fas fa-check me-2"></i>Copied!';
            copyBtn.classList.remove('btn-primary');
            copyBtn.classList.add('btn-success');

            setTimeout(() => {
                copyBtn.innerHTML = originalText;
                copyBtn.classList.remove('btn-success');
                copyBtn.classList.add('btn-primary');
            }, 2000);
        }).catch(err => {
            console.error('Failed to copy text: ', err);
            fallbackCopyText(textContent);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyText(textContent);
    }
}

// Fallback copy method for older browsers
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        alert('Text copied to clipboard!');
    } catch (err) {
        console.error('Fallback copy failed: ', err);
        alert('Failed to copy text. Please select and copy manually.');
    }

    document.body.removeChild(textArea);
}

// AJAX File Upload Functions
function startAjaxFileUpload() {
    const form = document.getElementById('uploadFileForm');
    const formData = new FormData(form);

    // Close upload modal
    const uploadModal = bootstrap.Modal.getInstance(document.getElementById('addFileModal'));
    if (uploadModal) {
        uploadModal.hide();
    }

    // Show progress modal
    const progressModal = new bootstrap.Modal(document.getElementById('progressModal'));
    progressModal.show();

    // Initialize progress
    updateProgressModal(0, 'uploading', 'Starting upload...', form.querySelector('input[name="file"]').files[0].name);

    // Make AJAX request
    fetch('<?= base_url('applicant/profile/upload-file-ajax') ?>', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Small delay before starting progress checking to show initial upload progress
            setTimeout(() => {
                checkUploadProgress(data.process_id);
            }, 500);
        } else {
            updateProgressModal(0, 'error', data.message || 'Upload failed');
            showProgressCloseButton();
        }
    })
    .catch(error => {
        console.error('Upload error:', error);
        updateProgressModal(0, 'error', 'Network error occurred');
        showProgressCloseButton();
    });
}

function checkUploadProgress(processId) {
    const checkProgress = () => {
        fetch(`<?= base_url('applicant/profile/check-upload-progress') ?>/${processId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.progress) {
                const progress = data.progress;
                updateProgressModal(
                    progress.progress,
                    progress.step,
                    progress.message,
                    progress.file_name
                );

                // Update step icons
                updateStepIcons(progress.step);

                // Show chunk info for PDF splitting
                if (progress.step === 'splitting' || progress.step === 'extracting') {
                    showChunkInfo(progress.message);
                }

                // Check if completed or failed
                if (progress.step === 'completed') {
                    updateProgressModal(100, 'completed', 'File processed successfully!');
                    showProgressCloseButton();
                    setTimeout(() => {
                        location.reload(); // Refresh to show new file
                    }, 2000);
                } else if (progress.step === 'error') {
                    showProgressCloseButton();
                } else {
                    // Continue checking with longer interval for smoother progress
                    setTimeout(checkProgress, 1500);
                }
            } else {
                updateProgressModal(0, 'error', 'Progress tracking failed');
                showProgressCloseButton();
            }
        })
        .catch(error => {
            console.error('Progress check error:', error);
            updateProgressModal(0, 'error', 'Failed to check progress');
            showProgressCloseButton();
        });
    };

    checkProgress();
}

function updateProgressModal(progress, step, message, fileName = null) {
    // Update progress bar
    const progressBar = document.getElementById('progressBar');
    progressBar.style.width = progress + '%';

    // Update percentage
    document.getElementById('progressPercent').textContent = progress + '%';

    // Update message
    document.getElementById('progressMessage').textContent = message;

    // Update file name if provided
    if (fileName) {
        document.getElementById('progressFileName').textContent = fileName;
    }

    // Handle spinner visibility
    const spinner = document.getElementById('progressSpinner');
    if (step === 'completed' || step === 'error') {
        spinner.style.display = 'none';
    } else {
        spinner.style.display = 'inline-block';
    }

    // Update progress bar color based on step
    progressBar.className = 'progress-bar progress-bar-striped progress-bar-animated';
    if (step === 'completed') {
        progressBar.classList.add('bg-success');
    } else if (step === 'error') {
        progressBar.classList.add('bg-danger');
    } else {
        progressBar.classList.add('bg-primary');
    }
}

function updateStepIcons(currentStep) {
    // Reset all icons
    const steps = ['stepUpload', 'stepAnalyze', 'stepExtract', 'stepSave'];
    steps.forEach(stepId => {
        const icon = document.getElementById(stepId).querySelector('i');
        icon.className = icon.className.replace('text-primary text-success', 'text-muted');
    });

    // Update current and completed steps
    const stepMap = {
        'uploading': ['stepUpload'],
        'analyzing': ['stepUpload', 'stepAnalyze'],
        'splitting': ['stepUpload', 'stepAnalyze'],
        'processing': ['stepUpload', 'stepAnalyze'],
        'extracting': ['stepUpload', 'stepAnalyze', 'stepExtract'],
        'combining': ['stepUpload', 'stepAnalyze', 'stepExtract'],
        'saving': ['stepUpload', 'stepAnalyze', 'stepExtract', 'stepSave'],
        'completed': ['stepUpload', 'stepAnalyze', 'stepExtract', 'stepSave']
    };

    const activeSteps = stepMap[currentStep] || [];
    activeSteps.forEach(stepId => {
        const icon = document.getElementById(stepId).querySelector('i');
        if (currentStep === 'completed') {
            icon.className = icon.className.replace('text-muted', 'text-success');
        } else {
            icon.className = icon.className.replace('text-muted', 'text-primary');
        }
    });
}

function showChunkInfo(message) {
    const chunkInfo = document.getElementById('chunkInfo');
    const chunkMessage = document.getElementById('chunkMessage');

    if (message.includes('chunk') || message.includes('splitting')) {
        chunkMessage.textContent = message;
        chunkInfo.classList.remove('d-none');
    }
}

function showProgressCloseButton() {
    document.getElementById('progressCloseBtn').classList.remove('d-none');
}

function confirmDeleteFile(id) {
    if (confirm('Are you sure you want to delete this file?')) {
        // Save scroll position before submission
        saveScrollPosition();

        // Create a form and submit it
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?= base_url('applicant/profile/delete-file') ?>/${id}`;

        // Add CSRF token
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = '<?= csrf_token() ?>';
        csrfInput.value = '<?= csrf_hash() ?>';
        form.appendChild(csrfInput);

        document.body.appendChild(form);
        form.submit();
    }
}

$(document).ready(function() {
    // Handle storage access
    handleStorageError();

    // Check if we just uploaded a file and need to start progress checking
    const processId = '<?= session()->getFlashdata('process_id') ?>';

    if (processId) {
        // Start checking progress for the new upload
        checkExtractionProgress(processId);
    }

    // Restore scroll position after page load
    restoreScrollPosition();

    // Handle hash-based navigation
    handleHashNavigation();

    // Handle AJAX file upload
    $('#uploadBtn').on('click', function(e) {
        e.preventDefault();

        const form = document.getElementById('uploadFileForm');
        if (!validateFileUpload(form)) {
            return false;
        }

        // Start AJAX upload
        startAjaxFileUpload();
    });

    // Add scroll position saving to all forms (except upload form which is now AJAX)
    $('form').on('submit', function(e) {
        const formId = $(this).attr('id');

        // Skip upload form as it's now handled by AJAX
        if (formId === 'uploadFileForm') {
            e.preventDefault();
            return false;
        }

        // Handle file edit validation
        if (formId === 'editFileForm') {
            if (!validateFileEdit(this)) {
                e.preventDefault();
                return false;
            }
            // Close modal before submission
            const modal = bootstrap.Modal.getInstance(document.getElementById('editFileModal'));
            if (modal) {
                modal.hide();
            }
        }

        saveScrollPosition();

        // For specific forms, also set the hidden input
        const scrollPosition = window.pageYOffset || document.documentElement.scrollTop;

        if (formId === 'additionalForm') {
            $('#additional_scroll_position').val(scrollPosition);
        } else if (formId === 'personalForm') {
            $('#personal_scroll_position').val(scrollPosition);
        } else if (formId === 'uploadFileForm') {
            $('#upload_scroll_position').val(scrollPosition);
        } else if (formId === 'editFileForm') {
            $('#edit_scroll_position').val(scrollPosition);
        }
    });

    // Handle marital status change
    $('#maritalStatus').on('change', function() {
        if ($(this).val() === 'married') {
            $('.marriage-details').slideDown();
        } else {
            $('.marriage-details').slideUp();
        }
    });

    // Trigger initial marital status check
    $('#maritalStatus').trigger('change');

    // Handle adding new child
    $('#addChild').on('click', function() {
        const index = $('.child-entry').length;
        const newChild = `
            <div class="row g-2 mb-2 child-entry">
                <div class="col-md-4">
                    <input type="text" class="form-control" name="children[${index}][name]" placeholder="Child's Name">
                </div>
                <div class="col-md-4">
                    <input type="date" class="form-control" name="children[${index}][dob]">
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="children[${index}][gender]">
                        <option value="">Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                    </select>
                </div>
                <div class="col-md-1">
                    <button type="button" class="btn btn-danger btn-sm remove-child"><i class="fas fa-times"></i></button>
                </div>
            </div>
        `;
        $('#childrenContainer').append(newChild);
    });

    // Handle removing child
    $(document).on('click', '.remove-child', function() {
        $(this).closest('.child-entry').remove();
    });

    // Removed AJAX form submission - using standard form submission now

    // Smooth scroll to sections
    $('.list-group-item').on('click', function(e) {
        e.preventDefault();
        const target = $($(this).attr('href'));
        $('html, body').animate({
            scrollTop: target.offset().top - 20
        }, 500);
    });

    // Photo upload handling - using standard form submission
    $('input[name="id_photo"]').on('change', function() {
        const file = this.files[0];
        if (file) {
            // Save scroll position before submission
            saveScrollPosition();

            // Submit the form normally
            $('#photoForm')[0].submit();
        }
    });

    // Initialize the modal
    const experienceModal = new bootstrap.Modal(document.getElementById('experienceModal'));

    // Handle form submission - using standard form submission
    $('#saveExperience').on('click', function() {
        const form = document.getElementById('experienceForm');
        const formData = new FormData(form);
        const isEdit = formData.get('id') ? true : false;

        // Save scroll position before submission
        saveScrollPosition();

        // Set form action and submit normally
        form.action = isEdit ? '<?= base_url('applicant/profile/update-experience') ?>' : '<?= base_url('applicant/profile/add-experience') ?>';
        form.method = 'POST';
        form.submit();
    });

    // Function to add experience
    window.addExperience = function() {
        document.getElementById('experienceForm').reset();
        document.getElementById('experience_id').value = '';
        document.getElementById('experienceModalLabel').textContent = 'Add Work Experience';
        experienceModal.show();
    };

    // Function to edit experience
    window.editExperience = function(id) {
        const experience = experiences.find(exp => exp.id === id);
        if (!experience) return;

        document.getElementById('experience_id').value = experience.id;
        document.getElementById('position').value = experience.position;
        document.getElementById('employer').value = experience.employer;
        document.getElementById('employer_contacts_address').value = experience.employer_contacts_address || '';
        document.getElementById('date_from').value = formatDateForInput(experience.date_from);
        document.getElementById('date_to').value = formatDateForInput(experience.date_to);
        document.getElementById('work_description').value = experience.work_description || '';
        document.getElementById('achievements').value = experience.achievements || '';

        document.getElementById('experienceModalLabel').textContent = 'Edit Work Experience';
        experienceModal.show();
    };

    // Function to delete experience - using standard form submission
    window.deleteExperience = function(id) {
        if (confirm('Are you sure you want to delete this work experience?')) {
            // Save scroll position before submission
            saveScrollPosition();

            // Create a form and submit it
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `<?= base_url('applicant/profile/delete-experience') ?>/${id}`;

            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '<?= csrf_token() ?>';
            csrfInput.value = '<?= csrf_hash() ?>';
            form.appendChild(csrfInput);

            document.body.appendChild(form);
            form.submit();
        }
    };

    // Edit Education Form Handler - using standard form submission
    $('[id^=editEducationForm]').each(function() {
        $(this).on('submit', function(e) {
            // Save scroll position before submission
            saveScrollPosition();

            // Allow normal form submission (remove preventDefault)
            // Form will submit normally to its action URL
        });
    });

    // Handle extracted text modal
    $(document).on('click', '.view-extracted-text', function() {
        const fileId = $(this).data('file-id');
        const file = filesData.find(f => f.id == fileId);

        if (file) {
            const extractedText = file.file_extracted_texts || 'No text extracted';

            document.getElementById('extracted_text_filename').textContent = file.file_title;

            // Set both formatted and raw content
            document.getElementById('extracted_text_formatted').innerHTML = renderMarkdown(extractedText);
            document.getElementById('extracted_text_raw').textContent = extractedText;

            // Reset to formatted view
            document.getElementById('markdownView').checked = true;
            document.getElementById('extracted_text_formatted').style.display = 'block';
            document.getElementById('extracted_text_raw').style.display = 'none';
        } else {
            document.getElementById('extracted_text_filename').textContent = 'Unknown File';
            document.getElementById('extracted_text_formatted').innerHTML = 'Error: File data not found';
            document.getElementById('extracted_text_raw').textContent = 'Error: File data not found';
        }
    });

    // Handle view mode toggle
    $(document).on('change', 'input[name="viewMode"]', function() {
        if (this.id === 'markdownView') {
            document.getElementById('extracted_text_formatted').style.display = 'block';
            document.getElementById('extracted_text_raw').style.display = 'none';
        } else {
            document.getElementById('extracted_text_formatted').style.display = 'none';
            document.getElementById('extracted_text_raw').style.display = 'block';
        }
    });

    // Reset upload form when modal is shown
    $('#addFileModal').on('show.bs.modal', function() {
        document.getElementById('uploadFileForm').reset();
    });

    // Handle progress modal close
    $('#progressCloseBtn').on('click', function() {
        const progressModal = bootstrap.Modal.getInstance(document.getElementById('progressModal'));
        if (progressModal) {
            progressModal.hide();
        }
    });

    // Reset progress modal when hidden
    $('#progressModal').on('hidden.bs.modal', function() {
        // Reset progress modal state
        updateProgressModal(0, 'uploading', 'Initializing...');
        document.getElementById('chunkInfo').classList.add('d-none');
        document.getElementById('progressCloseBtn').classList.add('d-none');

        // Reset step icons
        const steps = ['stepUpload', 'stepAnalyze', 'stepExtract', 'stepSave'];
        steps.forEach(stepId => {
            const icon = document.getElementById(stepId).querySelector('i');
            icon.className = icon.className.replace('text-primary text-success', 'text-muted');
        });
    });
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?>
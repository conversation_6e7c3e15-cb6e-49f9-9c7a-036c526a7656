<?php
/**
 * Background File Processing Script
 * This script runs independently to process files with visible progress updates
 */

// Get command line arguments
if ($argc < 6) {
    echo "Usage: php process_file_background.php <process_id> <file_path> <applicant_id> <file_title> <file_description>\n";
    exit(1);
}

$processId = $argv[1];
$filePath = $argv[2];
$applicantId = $argv[3];
$fileTitle = $argv[4];
$fileDescription = $argv[5];

// Bootstrap CodeIgniter environment
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
define('SYSTEMPATH', __DIR__ . '/../system/');
define('APPPATH', __DIR__ . '/../app/');
define('WRITEPATH', __DIR__ . '/../writable/');
define('ENVIRONMENT', 'development');

// Load CodeIgniter
require_once SYSTEMPATH . 'bootstrap.php';

// Start session
session_start();

// Load helper
helper('GeminiAI');

// Initialize database
$db = \Config\Database::connect();

echo "=== Background File Processing Started ===\n";
echo "Process ID: {$processId}\n";
echo "File Path: {$filePath}\n";
echo "Applicant ID: {$applicantId}\n";
echo "Started at: " . date('Y-m-d H:i:s') . "\n";
echo "==========================================\n\n";

try {
    // Step 1: Initial processing (40%)
    updateBackgroundProgress($processId, 40, 'processing', 'Starting AI text extraction...');
    sleep(1);
    
    // Step 2: File validation (42%)
    updateBackgroundProgress($processId, 42, 'analyzing', 'Validating file format...');
    sleep(1);
    
    // Step 3: File analysis (45%)
    updateBackgroundProgress($processId, 45, 'analyzing', 'Analyzing document structure...');
    sleep(1);
    
    // Step 4: Preparing for AI processing (48%)
    updateBackgroundProgress($processId, 48, 'processing', 'Preparing document for AI analysis...');
    sleep(1);
    
    // Extract text using Gemini AI with progress tracking
    $extractionResult = gemini_extract_text_from_file($filePath, $processId);
    
    $extractedText = null;
    $extractionStatus = 'failed';
    
    if ($extractionResult['success']) {
        $extractedText = $extractionResult['extracted_text'];
        $extractionStatus = 'completed';
        
        // Step 5: Post-processing (88%)
        updateBackgroundProgress($processId, 88, 'processing', 'Processing extracted text...');
        sleep(1);
        
        // Step 6: Validation (90%)
        updateBackgroundProgress($processId, 90, 'processing', 'Validating extracted content...');
        sleep(1);
        
        // Step 7: Preparing database entry (92%)
        updateBackgroundProgress($processId, 92, 'saving', 'Preparing database entry...');
        sleep(1);
        
    } else {
        // Update progress for failed extraction
        updateBackgroundProgress($processId, 0, 'error', 'Text extraction failed: ' . $extractionResult['message']);
        echo "ERROR: Text extraction failed - " . $extractionResult['message'] . "\n";
        exit(1);
    }

    // Step 8: Save to database (95%)
    updateBackgroundProgress($processId, 95, 'saving', 'Saving to database...');
    sleep(1);
    
    $data = [
        'applicant_id' => $applicantId,
        'file_title' => $fileTitle,
        'file_description' => $fileDescription,
        'file_path' => 'public/uploads/applicants/' . $applicantId . '/' . basename($filePath),
        'file_extracted_texts' => $extractedText,
        'created_by' => $applicantId
    ];

    // Insert into database
    $builder = $db->table('applicant_files');
    $result = $builder->insert($data);
    
    if (!$result) {
        updateBackgroundProgress($processId, 0, 'error', 'Database error: Failed to save file record');
        echo "ERROR: Database insertion failed\n";
        exit(1);
    }
    
    // Step 9: Finalizing (98%)
    updateBackgroundProgress($processId, 98, 'saving', 'Finalizing file record...');
    sleep(1);
    
    // Step 10: Final completion (100%)
    updateBackgroundProgress($processId, 100, 'completed', 'File processed successfully!');
    
    echo "SUCCESS: File processed successfully!\n";
    echo "Extracted text length: " . strlen($extractedText) . " characters\n";
    echo "Database record ID: " . $db->insertID() . "\n";
    
} catch (Exception $e) {
    updateBackgroundProgress($processId, 0, 'error', 'Processing error: ' . $e->getMessage());
    echo "ERROR: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Background Processing Completed ===\n";

/**
 * Update progress for background processing
 */
function updateBackgroundProgress($processId, $progress, $step, $message)
{
    $_SESSION["upload_progress_{$processId}"] = [
        'step' => $step,
        'progress' => $progress,
        'message' => $message,
        'file_name' => basename($GLOBALS['filePath'] ?? 'Unknown file'),
        'timestamp' => time()
    ];
    
    // Force session write
    session_write_close();
    session_start();
    
    echo "[{$progress}%] {$step}: {$message}\n";
}
?>
